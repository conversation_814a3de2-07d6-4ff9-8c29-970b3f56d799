# ---------------------------------------------------------------------------
# General Server Settings
# ---------------------------------------------------------------------------
# The defaults are set to be retail-like. If you modify any of these settings your server will deviate from being retail-like.
# Warning: 
# Please take extreme caution when changing anything. Also please understand what you are changing before you do so on a live server.

# ---------------------------------------------------------------------------
# Administrator
# ---------------------------------------------------------------------------

# Default Access Level. For example if you set 100, everyone will have 100 access level.
# Default: 0
DefaultAccessLevel = 0


# If True, only accounts with GM access can enter the server.
# Default: False
ServerGMOnly = False

# Enable GMs to have the glowing aura of a Hero character on login. 
# Notes:
#	GMs can do "///hero" on themselves and get this aura voluntarily.
#	It's advised to keep this off due to graphic lag.
# Default: False
GMHeroAura = False

# Whether GM logins in builder hide mode by default.
# Default: True
GMStartupBuilderHide = True

# Auto set invulnerable status to a GM on login.
# Default: False
GMStartupInvulnerable = True

# Auto set invisible status to a GM on login.
# Default: False
GMStartupInvisible = True

# Auto block private messages to a GM on login.
# Default: False
GMStartupSilence = False

# Auto list GMs in GM list (/gmlist) on login.
# Default: False
GMStartupAutoList = False

# Auto set diet mode on to a GM on login (affects your weight penalty).
# Default: False
GMStartupDietMode = False

# Item restrictions apply to GMs as well? (True = restricted usage)
# Default: True
GMItemRestriction = True

# Skill restrictions apply to GMs as well? (True = restricted usage)
# Default: True
GMSkillRestriction = True

# Allow GMs to drop/trade non-tradable and quest(drop only) items
# Default: False
GMTradeRestrictedItems = False

# Allow GMs to restart/exit while is fighting stance
# Default: True
GMRestartFighting = True

# Show the GM's name behind an announcement made by him
# example: "Announce: hi (HanWik)"
GMShowAnnouncerName = False

# Give special skills for every GM
# 7029,7041-7064,7088-7096,23238-23249 (Master's Blessing)
# Default: False
GMGiveSpecialSkills = False

# Give special aura skills for every GM
# 7029,23238-23249,23253-23296 (Master's Blessing)
# Default: False
GMGiveSpecialAuraSkills = False

# Debug html paths for GM characters.
# Default: True
GMDebugHtmlPaths = True

# In case you are not satisfied with the retail-like implementation of //gmspeed",
# with this config you can rollback it to the old custom L2J version of the GM Speed.
# Default: False
UseSuperHasteAsGMSpeed = False


# ---------------------------------------------------------------------------
# Server Security
# ---------------------------------------------------------------------------

#Logging settings. The following four settings, while enabled, will increase writing to your hard drive(s) considerably. Depending on the size of your server, the amount of players, and other factors, you may suffer a noticable performance hit.
# Default: False
LogChat = False

# Default: False
LogItems = True

# Log only Adena and equippable items if LogItems is enabled.
LogItemsSmallLog = False

# Log only item ids specified if LogItems is enabled.
LogItemsIdsOnly = False

# Log only item ids specified here, separated by commas.
# Default: 4356 (Gold Einhasad)
LogItemsIdsList = 4356

# Default: False
LogItemEnchants = True

# Default: False
LogSkillEnchants = True

# Default: False
GMAudit = False

# Check players for non-allowed skills
# Default: False
SkillCheckEnable = True

# If true, remove invalid skills from player and database.
# Report only, if false.
# Default: False
SkillCheckRemove = True

# Check also GM characters (only if SkillCheckEnable = True)
# Default: True
SkillCheckGM = False


# ---------------------------------------------------------------------------
# Optimization
# ---------------------------------------------------------------------------

# Items on ground management.
# Allow players to drop items on the ground.
# Default: True
AllowDiscardItem = True

# Delete dropped reward items from world after a specified amount of seconds. Disabled = 0.
# Default: 600 = 10 mins
# Default: 3600 = 1 hours
AutoDestroyDroppedItemAfter = 3600

# Time in seconds after which dropped herb will be auto-destroyed
# Default: 60
AutoDestroyHerbTime = 60

# List of item id that will not be destroyed (separated by "," like 57,5575,6673).
# Notes:
#	Make sure the lists do NOT CONTAIN trailing spaces or spaces between the numbers!
#	Items on this list will be protected regardless of the following options.
# Default: 0
ListOfProtectedItems = 0

# Cleans up the server database on startup.
# The bigger the database is, the longer it will take to clean up the database(the slower the server will start).
# Sometimes this ends up with 0 elements cleaned up, and a lot of wasted time on the server startup.
# If you want a faster server startup, set this to 'false', but its recommended to clean up the database from time to time.
# Default: True
DatabaseCleanUp = True

# This is the interval (in minutes), that the gameserver will update a players information such as location.
# The higher you set this number, there will be less character information saving so you will have less accessing of the database and your hard drive(s).
# The lower you set this number, there will be more frequent character information saving so you will have more access to the database and your hard drive(s).
# A value of 0 disables periodic saving.
# Independent of this setting the character is always saved after leaving the world.
# Default: 15
CharacterDataStoreInterval = 15

# This is the interval (in minutes), that the game server will update a clan's variables information into the database.
# The higher you set this number, there will be less clan's variables information saving so you will have less accessing of the database and your hard drive(s).
# The lower you set this number, there will be more frequent clan's variables information saving so you will have more access to the database and your hard drive(s).
# A value of 0 disables periodic saving.
# Default: 15
ClanVariablesStoreInterval = 15

# This enables the server to only update items when saving the character.
# Enabling this greatly reduces DB usage and improves performance.
# WARNING: This option causes item loss during crashes.
# Default: False
LazyItemsUpdate = False

# When enabled, this forces (even if using lazy item updates) the items owned by the character to be updated into DB when saving its character.
# Default: True
UpdateItemsOnCharStore = True

# Also delete from world misc. items dropped by players (all except equip-able items).
# Notes:
#	Works only if AutoDestroyDroppedItemAfter is greater than 0.
# Default: False
DestroyPlayerDroppedItem = False

# Destroy dropped equippable items (armor, weapon, jewelry).
# Notes:
#	Works only if DestroyPlayerDroppedItem = True
# Default: False
DestroyEquipableItem = False

# Make all items destroyable.
# If enabled players can destroy all items!!!
DestroyAllItems = False

# Save dropped items into the database for restoring after restart.
# Default: False
SaveDroppedItem = False

# Enable/Disable the emptying of the stored dropped items table after items are loaded into memory (safety setting).
# If the server crashed before saving items, on next start old items will be restored and players may already have picked up some of them so this will prevent duplicates.
# Default: False
EmptyDroppedItemTableAfterLoad = False

# Time interval in minutes to save in DB items on ground. Disabled = 0.
# Notes:
#	If SaveDroppedItemInterval is disabled, items will be saved into the database only at server shutdown.
# Default: 60
SaveDroppedItemInterval = 60

# Delete all saved items from the database on next restart?
# Notes:
#	Works only if SaveDroppedItem = False.
# Default: False
ClearDroppedItemTable = False

# Order NPC QuestLink list by QuestId.
# Retail: False (but ugly)
OrderQuestListByQuestId = True

# Delete invalid quest from players.
# Default: False
AutoDeleteInvalidQuestData = False

# Reward players with experience boost buff upon finishing a Story Quest.
# Retail: True
StoryQuestRewardBuff = True

# Allow creating multiple non-stackable items at one time?
# Default: True
MultipleItemDrop = True

# Enable/Disable html caching.
# True = Load all html's into cache on server startup.
# False = Load html's into cache only on first time html is requested.
# Recommended for live servers: True
# Recommended for development: False
HtmCache = False

# Check if html files contain non ASCII characters.
# Default = True
CheckHtmlEncoding = True

# Automatic removal of -h parameter from Chat and Quest bypasses.
# Prevents flickering from closing the dialog window when bypass is used.
# Default: True
HideBypassRemoval = True

# Minimum and maximum variables in seconds for NPC animation delay.
# You must keep MinNpcAnimation lower or equal to MaxNpcAnimation.
# Set values to 0 for disabling random animations.
# Default: 5
MinNpcAnimation = 5
# Default: 60
MaxNpcAnimation = 60
# Default: 5
MinMonsterAnimation = 5
# Default: 60
MaxMonsterAnimation = 60

# Grid options: Grids can turn themselves on and off.  This also affects the loading and processing of all AI tasks and (in the future) geodata within this grid.
# Turn on for a grid with a person in it is immediate, but it then turns on the 8 neighboring grids based on the specified number of seconds.
# Turn off for a grid and neighbors occurs after the specified number of seconds have passed during which a grid has had no players in or in any of its neighbors.
# The always on option allows to ignore all this and let all grids be active at all times (not suggested).
# Default: False
GridsAlwaysOn = False

# Default: 1
GridNeighborTurnOnTime = 1

# Default: 90
GridNeighborTurnOffTime = 90

# Correct buylist and multisell prices when lower than sell price.
# Default: True
CorrectPrices = False

# Item limit on multisell transaction.
# Max client allowed 999999
MultisellAmountLimit = 10000


# ---------------------------------------------------------------------------
# Falling Damage
# ---------------------------------------------------------------------------

# Allow characters to receive damage from falling.
# Default: True
EnableFallingDamage = False


# ---------------------------------------------------------------------------
# Features
# ---------------------------------------------------------------------------

# Peace Zone Modes:
# 0 = Peace All the Time
# 1 = PVP During Siege for siege participants
# 2 = PVP All the Time
# Default: 0
PeaceZoneMode = 0

# Global Chat.
# Available Options: ON, OFF, GM, GLOBAL
# Default: ON
GlobalChat = ON

# Trade Chat.
# Available Options: ON, OFF, GM, GLOBAL
# Default: ON
TradeChat = GLOBAL

# Minimum level for chat, 0 = disable
# Default: 0
MinimumChatLevel = 0

# If you are experiencing problems with Warehouse transactions, feel free to disable them here.
# Default: True
AllowWarehouse = True

# Default: True
AllowRefund = True

# Default: True
AllowMail = True

# Default: True
AllowAttachments = True

# If True player can try on weapon and armor in shop.
# Default: True
AllowWear = True

# Default: 5
WearDelay = 5

#Adena cost to try on an item.
# Default: 10
WearPrice = 10


# ---------------------------------------------------------------------------
# Instances
# ---------------------------------------------------------------------------
# Restores the player to their previous instance (ie. an instanced area/dungeon) on EnterWorld.
# Default: False
RestorePlayerInstance = False

# When a player dies, is removed from instance after a fixed period of time.
# Time in minutes.
# Default: 1
EjectDeadPlayerTime = 1

# When is instance finished, is set time to destruction currency instance.
# Time in minutes.
# Default: 5
DefaultFinishTime = 5

# ---------------------------------------------------------------------------
# Misc Settings
# ---------------------------------------------------------------------------

# Default: True
AllowRace = False

# Default: True
AllowWater = True

# Default: True
AllowFishing = True

# Default: True
AllowBoat = True

# Boat broadcast radius.
# If players getting annoyed by boat shouts then radius can be decreased.
# Default: 20000
BoatBroadcastRadius = 20000

# Classic: False
AllowCursedWeapons = False

# Show "data/html/servnews.htm" when a character enters world.
# Default: False
ShowServerNews = False

# Enable the Community Board.
# Default: True
EnableCommunityBoard = True

# Default Community Board page.
# Default: _bbshome
BBSDefault = _bbshome

# Enable chat filter
# Default = False
UseChatFilter = False

# Replace filter words with following chars
ChatFilterChars = ^_^

# Banchat for channels, split ";"
# GENERAL (white)
# SHOUT (!)
# TELL (")
# PARTY (#)
# CLAN (@)
# GM (//gmchat)
# PETITION_PLAYER (*)
# PETITION_GM (*)
# TRADE (+)
# ALLIANCE ($)
# ANNOUNCEMENT
# BOAT
# FRIEND
# MSNCHAT
# PARTYMATCH_ROOM
# PARTYROOM_COMMANDER (Yellow)
# PARTYROOM_ALL (Red)
# HERO_VOICE (%)
# CRITICAL_ANNOUNCE
# SCREEN_ANNOUNCE
# BATTLEFIELD
# MPCC_ROOM
# NPC_GENERAL
# NPC_SHOUT
# NEW_TELL
# WORLD (&)
# Default: GENERAL;SHOUT;GLOBAL;TRADE;HERO_VOICE;WHISPER
BanChatChannels = GENERAL;SHOUT;WORLD;TRADE;HERO_VOICE;WHISPER


# ---------------------------------------------------------------------------
# World chat settings
# ---------------------------------------------------------------------------
# World Chat.
# Default: True
WorldChatEnabled = True

# The minimum level to use this chat.
# Default: 20
WorldChatMinLevel = 60

# The amount of points player will have at his disposal every day.
# Default: 3
WorldChatPointsPerDay = 3

# The delay player must wait before sending new world chat message.
# Note: Value is in seconds
# Default: 20secs
WorldChatInterval = 20secs


# ---------------------------------------------------------------------------
# Manor
# ---------------------------------------------------------------------------

# Classic: False
AllowManor = False

# Manor refresh time in military hours.
# Default: 20 (8pm)
AltManorRefreshTime = 20

# Manor refresh time (minutes).
# Default: 00 (start of the hour)
AltManorRefreshMin = 00

# Manor period approve time in military hours.
# Default: 4 (4am)
AltManorApproveTime = 4

# Manor period approve time (minutes).
# Default: 30
AltManorApproveMin = 30

# Manor maintenance time (minutes).
# Default: 6
AltManorMaintenanceMin = 6

# Manor Save Type.
# True = Save data into the database after every action
# Default: False
AltManorSaveAllActions = False

# Manor Save Period (used only if AltManorSaveAllActions = False)
# Default: 2 (hour)
AltManorSavePeriodRate = 2


# ---------------------------------------------------------------------------
# Item Auction
# ---------------------------------------------------------------------------

#
AltItemAuctionEnabled = False

# Number of days before auction cleared from database with all bids.
# Default: 14
AltItemAuctionExpiredAfter = 14

# Auction extends to specified amount of seconds if one or more new bids added.
# By default auction extends only two times, by 5 and 3 minutes, this custom value used after it.
# Values higher than 60s is not recommended.
# Default: 0
AltItemAuctionTimeExtendsOnBid = 0


 ---------------------------------------------------------------------------
# Punishment
# ---------------------------------------------------------------------------

# Player punishment for illegal actions:
# BROADCAST - broadcast warning to GMs only
# KICK - kick player (default)
# KICKBAN - kick and ban player
# JAIL - jail player
DefaultPunish = KICK

# This setting typically specifies the duration of the above punishment.
# Default: 0 (automatically sets to 100 years)
DefaultPunishParam = 0

# Apply default punish if player buy items for zero Adena.
# Default: True
OnlyGMItemsFree = True

# Jail is a PvP zone.
# Default: False
JailIsPvp = False

# Disable all chat in jail (except normal one)
# Default: True
JailDisableChat = True

# Disable all transaction in jail
# Trade/Store/Drop
# Default: False
JailDisableTransaction = False


# ---------------------------------------------------------------------------
# Custom Components
# ---------------------------------------------------------------------------

# Default: False
CustomNpcData = True

# Default: False
CustomSkillsLoad = True

# Default: False
CustomItemsLoad = True

# Default: False
CustomMultisellLoad = True

# Default: False
CustomBuyListLoad = True


# ---------------------------------------------------------------------------
# Teleport Bookmark Settings
# ---------------------------------------------------------------------------
# Consume item for teleporting.
# No item consume: -1
# Default: 20033  (Teleport Flag)
BookmarkConsumeItemId = 20033


# ---------------------------------------------------------------------------
# Birthday Event Settings
# ---------------------------------------------------------------------------

# Gift sent with Mail System
# Default: 7541
AltBirthdayGift = 7541

# Mail Subject
AltBirthdayMailSubject = Happy Birthday!

# Mail Content
# $c1: Player name
# $s1: Age
AltBirthdayMailText = Hello Adventurer!! Seeing as you're one year older now, I thought I would send you some birthday cheer :) Please find your birthday pack attached. May these gifts bring you joy and happiness on this very special day.\n\nSincerely, Alegria


# ---------------------------------------------------------------------------
# Bot Report Button settings
# ---------------------------------------------------------------------------

# Enable the bot report button on the desired game servers.
# Default: True
EnableBotReportButton = True

# Report points restart hour. Format: HH:MM ( PM mode, 24 hours clock)
# Default: 00:00
BotReportPointsResetHour = 00:00

# Delay between reports from the same player (in minutes)
# Default: 30 minutes
BotReportDelay = 30

# Allow players from the same clan to report the same bot
# Default: False
AllowReportsFromSameClanMembers = False


# ---------------------------------------------------------------------------
# Auto Play Settings
# ---------------------------------------------------------------------------

# Default: True
EnableAutoPlay = True

# Default: True
EnableAutoPotion = True

# Default: True
EnableAutoSkill = True

# Default: True
EnableAutoItem = True

# Default: True
EnableAutoPetPotion = True

# Resume auto play upon enter game.
# Retail: False
ResumeAutoPlay = False

# Assist party leader.
# When in party, target what the leader is targeting.
# Retail: False
AssistLeader = True


# ---------------------------------------------------------------------------
# Developer Settings
# ---------------------------------------------------------------------------
# Do not touch these if you do not know what you are doing.
# These settings are for debugging servers ONLY. They are not meant for LIVE servers.

# Html action cache debugging
# Default: False
HtmlActionCacheDebug = False

# Default: False
Developer = True

# Don't load quests.
# Default: False
AltDevNoQuests = False

# Don't load spawntable.
# Default: False
AltDevNoSpawns = False

# Show quests while loading them.
# Default: False
AltDevShowQuestsLoadInLogs = False

# Show scripts while loading them.
# Default: False
AltDevShowScriptsLoadInLogs = False

# Debug client packets.
# Default: False
DebugClientPackets = False

# Debug ex-client packets.
# Default: False
DebugExClientPackets = False

# Debug server packets.
# Default: False
DebugServerPackets = False

# Debug unknown packets.
# Default: True
DebugUnknownPackets = True

# Excluded packet list.
# Packet names that are excluded from debugging, separated by commas.
ExcludedPacketList = AbnormalStatusUpdate, AcquireSkillList, Attack, AutoAttackStart, AutoAttackStop, DeleteObject, ExAutoSoulShot, ExPacket, ExStorageMaxCount, ExUserInfoAbnormalVisualEffect, ExUserInfoEquipSlot, MoveToLocation, NpcInfo, NpcSay, SkillCoolTime, SocialAction, StatusUpdate, UserInfo, ValidatePosition
