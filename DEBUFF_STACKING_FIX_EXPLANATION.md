# Sửa đổi hệ thống Debuff Stacking

## Vấn đề ban đầu
Trong hệ thống cũ, khi có debuff mới cùng loại (cùng AbnormalType), game chỉ so sánh `abnormalLevel` để quyết định có ghi đè hay không. Điều này dẫn đến việc:
- Debuff mới luôn reset thời gian của debuff cũ nếu có cùng hoặc cao hơn abnormal level
- Người chơi bị "spam" debuff liên tụ<PERSON>, làm thời gian debuff bị kéo dài

## Giải pháp đã triển khai

### Thay đổi trong `EffectList.java`
Đã sửa đổi logic trong method `addActive()` để:

1. **Kiểm tra loại effect**: Phân biệt debuff và buff/effect khác
2. **Kiểm tra điều kiện ghi đè cho debuff**:
   - <PERSON><PERSON><PERSON> thời gian còn lại của debuff hiện tại: `existingInfo.getTime()`
   - Chỉ ghi đè nếu:
     - Debuff mới có abnormal level cao hơn, HOẶC
     - Debuff hiện tại đã hết thời gian (thời gian còn lại <= 0)

### Logic mới cho debuff:
```java
if (skill.isDebuff())
{
    final int existingRemainingTime = existingInfo.getTime();

    // Override only if new effect has higher abnormal level OR existing debuff has expired
    if ((skill.getAbnormalLevel() > existingSkill.getAbnormalLevel()) ||
        (existingRemainingTime <= 0))
    {
        shouldOverride = true;
    }
}
```

## Kết quả mong đợi

### Trước khi sửa:
- Player A stun Player B trong 10 giây
- Sau 5 giây, Player C stun Player B trong 10 giây
- Kết quả: Player B bị stun thêm 10 giây nữa (tổng 15 giây)

### Sau khi sửa:
- Player A stun Player B trong 10 giây
- Sau 5 giây, Player C stun Player B trong 10 giây
- Kết quả: Debuff của Player C bị từ chối vì debuff của Player A vẫn còn 5 giây

### Trường hợp debuff hết thời gian:
- Player A stun Player B trong 10 giây
- Sau 10 giây (debuff hết), Player C stun Player B trong 5 giây
- Kết quả: Debuff của Player C được áp dụng vì debuff cũ đã hết

## Tác động
- **Buff/Effect khác**: Không thay đổi, vẫn sử dụng logic cũ
- **Debuff**: Áp dụng logic mới, ngăn chặn spam debuff
- **Tương thích ngược**: Hoàn toàn tương thích với code hiện tại

## Tóm tắt logic mới
1. **Debuff cùng level, debuff hiện tại vẫn còn thời gian** → Từ chối
2. **Debuff cùng level, debuff hiện tại đã hết** → Ghi đè
3. **Debuff level cao hơn** → Luôn ghi đè
4. **Debuff level thấp hơn** → Luôn từ chối
5. **Buff/Effect khác** → Hoạt động như cũ

## Test Cases cần kiểm tra

### Test Case 1: Debuff cùng level, debuff hiện tại vẫn còn thời gian
**Setup:**
- Skill A: Stun (ID: 4726) - abnormalLevel=1, abnormalTime=5s
- Skill B: Stun (ID: 4744) - abnormalLevel=1, abnormalTime=9s

**Scenario:**
1. Player A cast Skill A lên Player C → Player C bị stun 5s
2. Sau 2s, Player B cast Skill B lên Player C
3. **Kết quả mong đợi:** Skill B bị từ chối vì debuff hiện tại vẫn còn 3s
4. Player C tiếp tục bị stun 3s như ban đầu

### Test Case 2: Debuff cùng level, debuff hiện tại đã hết
**Setup:**
- Skill A: Stun (ID: 4744) - abnormalLevel=1, abnormalTime=5s
- Skill B: Stun (ID: 4726) - abnormalLevel=1, abnormalTime=9s

**Scenario:**
1. Player A cast Skill A lên Player C → Player C bị stun 5s
2. Sau 5s (debuff hết), Player B cast Skill B lên Player C
3. **Kết quả mong đợi:** Skill B được áp dụng vì debuff cũ đã hết
4. Player C bị stun 9s mới

### Test Case 3: Debuff level cao hơn
**Setup:**
- Skill A: Stun (abnormalLevel=1, abnormalTime=9s)
- Skill B: Stun (abnormalLevel=2, abnormalTime=3s)

**Scenario:**
1. Player A cast Skill A → Target bị stun 9s
2. Sau 2s, Player B cast Skill B
3. **Kết quả mong đợi:** Skill B ghi đè vì level 2 > level 1
4. Target bị stun 3s mới (không quan tâm thời gian còn lại)

### Test Case 4: Debuff level thấp hơn
**Setup:**
- Skill A: Stun (abnormalLevel=2, abnormalTime=5s)
- Skill B: Stun (abnormalLevel=1, abnormalTime=10s)

**Scenario:**
1. Player A cast Skill A → Target bị stun 5s
2. Sau 1s, Player B cast Skill B
3. **Kết quả mong đợi:** Skill B bị từ chối vì level 1 < level 2
4. Target tiếp tục bị stun 4s như ban đầu

### Test Case 5: Buff/Effect khác (không phải debuff)
**Setup:**
- Buff A: Might (abnormalLevel=1, abnormalTime=1200s)
- Buff B: Might (abnormalLevel=1, abnormalTime=600s)

**Scenario:**
1. Player A cast Buff A → Target có buff 1200s
2. Sau 300s, Player B cast Buff B
3. **Kết quả mong đợi:** Buff B ghi đè (logic cũ) vì abnormalLevel >= existing
4. Target có buff 600s mới (reset thời gian như trước)

## Debugging
Khi `Config.DEVELOPER = true`, hệ thống sẽ log:
- "Debuff override: [SkillName] (higher level: X > Y)" - khi debuff có level cao hơn được ghi đè
- "Debuff override: [SkillName] (existing debuff expired, remaining: 0s)" - khi debuff cũ đã hết
- "Debuff rejected: [SkillName] (existing debuff still active: Xs remaining, level: Y)" - khi debuff bị từ chối
