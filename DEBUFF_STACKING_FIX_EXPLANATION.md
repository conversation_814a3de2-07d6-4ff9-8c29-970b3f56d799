# Sửa đổi hệ thống Debuff Stacking

## Vấn đề ban đầu
Trong hệ thống cũ, khi có debuff mới cùng loại (cùng AbnormalType), game chỉ so sánh `abnormalLevel` để quyết định có ghi đè hay không. Điều này dẫn đến việc:
- Debuff mới luôn reset thời gian của debuff cũ nếu có cùng hoặc cao hơn abnormal level
- Người chơi bị "spam" debuff li<PERSON>n tụ<PERSON>, làm thời gian debuff bị kéo dài

## Giải pháp đã triển khai

### Thay đổi trong `EffectList.java`
Đã sửa đổi logic trong method `addActive()` để:

1. **Kiểm tra loại effect**: Phân biệt debuff và buff/effect khác
2. **So sánh thời gian cho debuff**: 
   - <PERSON><PERSON><PERSON> thời gian còn lại của debuff hiện tại: `existingInfo.getTime()`
   - Lấy thời gian của debuff mới: `info.getAbnormalTime()`
   - Chỉ ghi đè nếu:
     - Debuff mới có abnormal level cao hơn, HOẶC
     - Debuff mới có cùng abnormal level nhưng thời gian dài hơn thời gian còn lại

### Logic mới cho debuff:
```java
if (skill.isDebuff())
{
    final int existingRemainingTime = existingInfo.getTime();
    final int newEffectTime = info.getAbnormalTime();
    
    // Override if new effect has higher abnormal level OR longer duration than remaining time
    if ((skill.getAbnormalLevel() > existingSkill.getAbnormalLevel()) || 
        (skill.getAbnormalLevel() == existingSkill.getAbnormalLevel() && newEffectTime > existingRemainingTime))
    {
        shouldOverride = true;
    }
}
```

## Kết quả mong đợi

### Trước khi sửa:
- Player A stun Player B trong 10 giây
- Sau 5 giây, Player C stun Player B trong 10 giây
- Kết quả: Player B bị stun thêm 10 giây nữa (tổng 15 giây)

### Sau khi sửa:
- Player A stun Player B trong 10 giây  
- Sau 5 giây, Player C stun Player B trong 10 giây
- Kết quả: Player B chỉ bị stun thêm 5 giây nữa (tổng 10 giây) vì debuff mới (10s) > thời gian còn lại (5s)

### Trường hợp debuff ngắn hơn:
- Player A stun Player B trong 10 giây
- Sau 2 giây, Player C stun Player B trong 5 giây  
- Kết quả: Debuff của Player C bị từ chối vì 5s < 8s (thời gian còn lại)

## Tác động
- **Buff/Effect khác**: Không thay đổi, vẫn sử dụng logic cũ
- **Debuff**: Áp dụng logic mới, ngăn chặn spam debuff
- **Tương thích ngược**: Hoàn toàn tương thích với code hiện tại

## Test Cases cần kiểm tra

### Test Case 1: Debuff cùng level, thời gian mới > thời gian còn lại
**Setup:**
- Skill A: Stun (ID: 4726) - abnormalLevel=1, abnormalTime=5s
- Skill B: Stun (ID: 4744) - abnormalLevel=1, abnormalTime=9s

**Scenario:**
1. Player A cast Skill A lên Player C → Player C bị stun 5s
2. Sau 2s, Player B cast Skill B lên Player C
3. **Kết quả mong đợi:** Skill B ghi đè vì 9s > 3s (thời gian còn lại)
4. Player C bị stun thêm 9s (không phải 3s + 9s = 12s)

### Test Case 2: Debuff cùng level, thời gian mới < thời gian còn lại
**Setup:**
- Skill A: Stun (ID: 4744) - abnormalLevel=1, abnormalTime=9s
- Skill B: Stun (ID: 4726) - abnormalLevel=1, abnormalTime=5s

**Scenario:**
1. Player A cast Skill A lên Player C → Player C bị stun 9s
2. Sau 2s, Player B cast Skill B lên Player C
3. **Kết quả mong đợi:** Skill B bị từ chối vì 5s < 7s (thời gian còn lại)
4. Player C tiếp tục bị stun 7s như ban đầu

### Test Case 3: Debuff level cao hơn
**Setup:**
- Skill A: Stun (abnormalLevel=1, abnormalTime=9s)
- Skill B: Stun (abnormalLevel=2, abnormalTime=3s)

**Scenario:**
1. Player A cast Skill A → Target bị stun 9s
2. Sau 2s, Player B cast Skill B
3. **Kết quả mong đợi:** Skill B ghi đè vì level 2 > level 1
4. Target bị stun 3s mới (không quan tâm thời gian còn lại)

### Test Case 4: Debuff level thấp hơn
**Setup:**
- Skill A: Stun (abnormalLevel=2, abnormalTime=5s)
- Skill B: Stun (abnormalLevel=1, abnormalTime=10s)

**Scenario:**
1. Player A cast Skill A → Target bị stun 5s
2. Sau 1s, Player B cast Skill B
3. **Kết quả mong đợi:** Skill B bị từ chối vì level 1 < level 2
4. Target tiếp tục bị stun 4s như ban đầu

### Test Case 5: Buff/Effect khác (không phải debuff)
**Setup:**
- Buff A: Might (abnormalLevel=1, abnormalTime=1200s)
- Buff B: Might (abnormalLevel=1, abnormalTime=600s)

**Scenario:**
1. Player A cast Buff A → Target có buff 1200s
2. Sau 300s, Player B cast Buff B
3. **Kết quả mong đợi:** Buff B ghi đè (logic cũ) vì abnormalLevel >= existing
4. Target có buff 600s mới (reset thời gian như trước)

## Debugging
Khi `Config.DEVELOPER = true`, hệ thống sẽ log:
- "Debuff override: [SkillName] (new:Xs > existing:Ys)" - khi debuff được ghi đè
- "Debuff rejected: [SkillName] (new:Xs <= existing:Ys)" - khi debuff bị từ chối
